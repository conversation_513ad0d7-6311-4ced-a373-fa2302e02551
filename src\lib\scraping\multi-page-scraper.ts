/**
 * Multi-Page Scraper for Scrape.do Integration
 * Configurable scraping of pricing, FAQ, features, and about pages
 */

import {
  MultiPageScrapingConfig,
  PageDiscoveryResult,
  ScrapingDecision,
  ScrapeResult,
  PageTypeConfig
} from './types';
import { scrapeDoClient } from './scrape-do-client';
import { costOptimizer } from './cost-optimizer';

export class MultiPageScraper {
  private config: MultiPageScrapingConfig;

  constructor(config?: Partial<MultiPageScrapingConfig>) {
    this.config = {
      enabled: true,
      mode: 'conditional', // Smart decision based on content and credits
      maxPagesPerTool: 4,
      creditThreshold: 100,

      pageTypes: {
        pricing: {
          enabled: true,
          priority: 'high',
          patterns: [
            '/pricing', '/price', '/plans', '/subscription',
            '/cost', '/buy', '/purchase', '/upgrade'
          ],
          selectors: [
            '.pricing', '.plans', '.subscription',
            '[class*="price"]', '[id*="pricing"]'
          ],
          required: true
        },
        faq: {
          enabled: true,
          priority: 'medium',
          patterns: [
            '/faq', '/help', '/support', '/questions',
            '/q-and-a', '/frequently-asked'
          ],
          selectors: [
            '.faq', '.help', '.support',
            '[class*="faq"]', '[id*="faq"]'
          ],
          required: false
        },
        features: {
          enabled: true,
          priority: 'high',
          patterns: [
            '/features', '/capabilities', '/functionality',
            '/what-we-do', '/services'
          ],
          selectors: [
            '.features', '.capabilities',
            '[class*="feature"]', '[id*="features"]'
          ],
          required: true
        },
        about: {
          enabled: true,
          priority: 'low',
          patterns: [
            '/about', '/about-us', '/company',
            '/story', '/mission', '/team'
          ],
          selectors: [
            '.about', '.company', '.story',
            '[class*="about"]', '[id*="about"]'
          ],
          required: false
        }
      },

      fallbackStrategy: {
        searchInMainPage: true,
        useNavigation: true,
        useSitemap: false
      },

      ...config
    };
  }

  /**
   * Discover and plan multi-page scraping strategy
   */
  async discoverAndPlanScraping(mainUrl: string, mainContent: string): Promise<ScrapingDecision> {
    if (!this.config.enabled) {
      return {
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'Multi-page scraping disabled'
      };
    }

    const discoveredPages = await this.discoverPages(mainUrl, mainContent);
    const currentCredits = await this.getCurrentCredits();

    return this.makeScrapingDecision(discoveredPages, currentCredits);
  }

  /**
   * Discover available pages for scraping
   */
  private async discoverPages(mainUrl: string, mainContent: string): Promise<PageDiscoveryResult[]> {
    const discovered: PageDiscoveryResult[] = [];
    const baseUrl = new URL(mainUrl).origin;

    for (const [pageType, pageConfig] of Object.entries(this.config.pageTypes)) {
      if (!pageConfig.enabled) continue;

      // Method 1: Check if content exists in main page
      if (this.config.fallbackStrategy.searchInMainPage) {
        const contentFound = this.findContentInMainPage(mainContent, pageConfig.selectors);
        if (contentFound.found) {
          discovered.push({
            pageType: pageType as any,
            url: mainUrl,
            confidence: contentFound.confidence,
            foundMethod: 'content',
            priority: pageConfig.priority,
            estimatedCredits: 0 // No additional scraping needed
          });
          continue; // Found in main page, no need to look for separate page
        }
      }

      // Method 2: Look for navigation links
      if (this.config.fallbackStrategy.useNavigation) {
        const navLinks = this.findNavigationLinks(mainContent, pageConfig.patterns);
        for (const link of navLinks) {
          discovered.push({
            pageType: pageType as any,
            url: this.resolveUrl(link.url, baseUrl),
            confidence: link.confidence,
            foundMethod: 'navigation',
            priority: pageConfig.priority,
            estimatedCredits: this.estimateScrapingCost(link.url)
          });
        }
      }

      // Method 3: Pattern-based URL construction
      if (discovered.filter(p => p.pageType === pageType).length === 0) {
        const constructedUrls = this.constructUrls(baseUrl, pageConfig.patterns);
        for (const url of constructedUrls) {
          discovered.push({
            pageType: pageType as any,
            url,
            confidence: 30, // Lower confidence for constructed URLs
            foundMethod: 'pattern',
            priority: pageConfig.priority,
            estimatedCredits: this.estimateScrapingCost(url)
          });
        }
      }
    }

    return discovered.sort((a, b) => {
      // Sort by priority, then confidence
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return b.confidence - a.confidence;
    });
  }

  /**
   * Make intelligent scraping decision based on credits and priorities
   */
  private makeScrapingDecision(pages: PageDiscoveryResult[], currentCredits: number): ScrapingDecision {
    const decision: ScrapingDecision = {
      scrapeNow: [],
      queueForLater: [],
      skipPages: [],
      reason: ''
    };

    let totalEstimatedCost = 0;

    // Prioritize high-priority and high-confidence pages
    for (const page of pages) {
      const pageCost = page.estimatedCredits;

      // Skip if we've reached max pages
      if (decision.scrapeNow.length >= this.config.maxPagesPerTool) {
        decision.skipPages.push(page);
        continue;
      }

      // Check credit availability
      if (totalEstimatedCost + pageCost > currentCredits || currentCredits < this.config.creditThreshold) {
        if (this.config.mode === 'queue_for_later') {
          decision.queueForLater.push(page);
        } else {
          decision.skipPages.push(page);
        }
        continue;
      }

      // Check if page is required or high priority
      const pageConfig = this.config.pageTypes[page.pageType];
      if (pageConfig.required || page.priority === 'high' || page.confidence > 70) {
        decision.scrapeNow.push(page);
        totalEstimatedCost += pageCost;
      } else if (this.config.mode === 'conditional' && page.confidence > 50) {
        decision.scrapeNow.push(page);
        totalEstimatedCost += pageCost;
      } else {
        decision.queueForLater.push(page);
      }
    }

    // Set reason for decision
    if (decision.scrapeNow.length === 0) {
      decision.reason = `Insufficient credits (${currentCredits}) or no high-confidence pages found`;
    } else {
      decision.reason = `Scraping ${decision.scrapeNow.length} pages (${totalEstimatedCost} credits), queuing ${decision.queueForLater.length}`;
    }

    return decision;
  }

  /**
   * Execute multi-page scraping based on decision
   */
  async executeMultiPageScraping(decision: ScrapingDecision): Promise<ScrapeResult[]> {
    const results: ScrapeResult[] = [];

    for (const page of decision.scrapeNow) {
      try {
        console.log(`Scraping ${page.pageType} page: ${page.url}`);
        
        const result = await costOptimizer.scrapeWithMaxCostOptimization(page.url);
        
        // Add page type metadata
        result.metadata = {
          creditsUsed: result.metadata?.creditsUsed || 0,
          requestType: result.metadata?.requestType || 'unknown',
          ...result.metadata,
          pageType: page.pageType,
          foundMethod: page.foundMethod,
          confidence: page.confidence
        };

        results.push(result);
      } catch (error) {
        console.error(`Failed to scrape ${page.pageType} page:`, error);
        results.push({
          success: false,
          content: '',
          error: (error as Error).message,
          timestamp: new Date().toISOString(),
          url: page.url,
          metadata: {
            creditsUsed: 0,
            requestType: 'failed',
            pageType: page.pageType,
            foundMethod: page.foundMethod,
            confidence: page.confidence
          }
        });
      }
    }

    return results;
  }

  /**
   * Find content in main page using selectors
   */
  private findContentInMainPage(content: string, selectors: string[]): { found: boolean; confidence: number } {
    let matchCount = 0;
    
    for (const selector of selectors) {
      // Convert CSS selector to regex pattern for content matching
      const pattern = new RegExp(`class=["'][^"']*${selector.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^"']*["']`, 'i');
      if (pattern.test(content)) {
        matchCount++;
      }
    }

    const confidence = Math.min(90, (matchCount / selectors.length) * 100);
    return {
      found: matchCount > 0,
      confidence
    };
  }

  /**
   * Find navigation links matching patterns
   */
  private findNavigationLinks(content: string, patterns: string[]): Array<{ url: string; confidence: number }> {
    const links: Array<{ url: string; confidence: number }> = [];
    
    // Extract all links from content
    const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>([^<]*)<\/a>/gi;
    let match;
    
    while ((match = linkRegex.exec(content)) !== null) {
      const url = match[1];
      const linkText = match[2].toLowerCase();
      
      for (const pattern of patterns) {
        if (url.includes(pattern) || linkText.includes(pattern.replace('/', ''))) {
          links.push({
            url,
            confidence: url.includes(pattern) ? 80 : 60 // Higher confidence for URL matches
          });
          break;
        }
      }
    }

    return links;
  }

  /**
   * Construct URLs based on patterns
   */
  private constructUrls(baseUrl: string, patterns: string[]): string[] {
    return patterns.map(pattern => `${baseUrl}${pattern}`);
  }

  /**
   * Estimate scraping cost for a URL
   */
  private estimateScrapingCost(url: string): number {
    // Use cost optimizer to estimate credits
    if (costOptimizer.categorizeUrlsByPattern([url]).neverEnhance.length > 0) {
      return 1; // Basic scraping
    } else if (costOptimizer.categorizeUrlsByPattern([url]).alwaysEnhance.length > 0) {
      return 5; // Enhanced scraping
    } else {
      return 2.5; // Average case
    }
  }

  /**
   * Get current available credits
   */
  private async getCurrentCredits(): Promise<number> {
    try {
      const stats = await scrapeDoClient.getUsageStatistics();
      return stats.remainingMonthlyRequests;
    } catch {
      return 0; // Conservative fallback
    }
  }

  /**
   * Resolve relative URLs
   */
  private resolveUrl(relativeUrl: string, baseUrl: string): string {
    try {
      return new URL(relativeUrl, baseUrl).href;
    } catch {
      return relativeUrl;
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<MultiPageScrapingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): MultiPageScrapingConfig {
    return { ...this.config };
  }
}

// Export singleton instance with default configuration
export const multiPageScraper = new MultiPageScraper();
