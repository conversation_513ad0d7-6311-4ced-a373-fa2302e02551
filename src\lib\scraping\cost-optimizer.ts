/**
 * Cost Optimization Module for Scrape.do API
 * Implements intelligent cost reduction strategies targeting 50-70% savings
 */

import {
  ScrapeR<PERSON>ult,
  CostBenefitAnalysis,
  CategorizedUrls,
  CostSavingsEstimate
} from './types';
import { scrapeDoClient } from './scrape-do-client';
import { ContentAnalyzer } from './content-analyzer';

export class CostOptimizer {
  private contentAnalyzer: ContentAnalyzer;

  // Never-enhance patterns - sites that work well with basic scraping (save 4 credits)
  private readonly NEVER_ENHANCE_PATTERNS = [
    /wikipedia\.org/i,
    /github\.com/i,
    /stackoverflow\.com/i,
    /reddit\.com/i,
    /medium\.com/i,
    /dev\.to/i,
    /docs\./i,                   // documentation sites
    /blog\./i,                   // blog sites
    /news\./i,                   // news sites
    /\.edu/i                     // educational sites
  ];

  // Always-enhance patterns - sites that need browser rendering (save 1 credit by skipping basic)
  private readonly ALWAYS_ENHANCE_PATTERNS = [
    /claude\.ai/i,
    /chat\.openai\.com/i,
    /bard\.google\.com/i,
    /notion\.so/i,
    /figma\.com/i,
    /canva\.com/i,
    /miro\.com/i,
    /discord\.com/i,
    /app\./i,                    // app.* subdomains
    /dashboard\./i,              // dashboard.* subdomains
    /admin\./i                   // admin.* subdomains
  ];

  constructor() {
    this.contentAnalyzer = new ContentAnalyzer();
  }

  /**
   * Main cost-optimized scraping method
   */
  async scrapeWithMaxCostOptimization(url: string): Promise<ScrapeResult> {
    console.log(`🎯 COST-OPTIMIZED SCRAPING: ${url}`);

    // Step 1: Check never-enhance patterns first (biggest savings - 80% cost reduction)
    if (this.matchesNeverEnhancePattern(url)) {
      console.log(`💰 PATTERN-SKIP: ${url} - using basic scraping only (1 credit)`);
      return await this.ultraBasicScrape(url);
    }

    // Step 2: Check always-enhance patterns (save 1 credit by skipping basic attempt)
    if (this.matchesAlwaysEnhancePattern(url)) {
      console.log(`⚡ PATTERN-ENHANCE: ${url} - using enhanced scraping directly (5 credits)`);
      return await this.enhancedScrapeOnly(url);
    }

    // Step 3: Unknown pattern - use ultra-aggressive detection
    return await this.intelligentCostOptimizedScrape(url);
  }

  /**
   * Intelligent cost-optimized scraping with content analysis
   */
  private async intelligentCostOptimizedScrape(url: string): Promise<ScrapeResult> {
    // Ultra-basic scrape with aggressive cost optimization
    const result = await this.ultraBasicScrape(url);

    // Aggressive content validation - keep if "good enough"
    if (this.isGoodEnoughContent(result.content)) {
      console.log(`✅ COST-OPTIMIZED: Keeping basic content for ${url} (1 credit)`);
      return result;
    }

    // Cost-benefit analysis before enhancement
    const costBenefit = this.calculateCostBenefit(result.content, url);
    if (!costBenefit.worthEnhancing) {
      console.log(`💰 COST-SAVE: Using basic content despite quality issues (${costBenefit.reasoning})`);
      return result;
    }

    // Only enhance if cost-benefit analysis shows it's worth 5x cost
    console.log(`⚡ ENHANCING: ${url} (5 credits) - ${costBenefit.reasoning}`);
    return await this.enhancedScrape(url);
  }

  /**
   * Ultra-basic scraping with maximum cost optimization
   */
  private async ultraBasicScrape(url: string): Promise<ScrapeResult> {
    return await scrapeDoClient.scrapePage(url, {
      useResidentialProxy: false,     // Datacenter only (1 credit)
      enableJSRendering: false,       // No browser (no 5x multiplier)
      outputFormat: 'markdown',       // AI-ready format
      blockResources: true,           // Block everything possible
      timeout: 15000,                 // Shorter timeout for cost savings
      customWaitTime: 1000,           // Minimal wait time
      deviceType: 'desktop'           // Standard device
    });
  }

  /**
   * Enhanced scraping for known difficult sites
   */
  private async enhancedScrapeOnly(url: string): Promise<ScrapeResult> {
    return await scrapeDoClient.scrapePage(url, {
      useResidentialProxy: false,     // Keep datacenter initially (cost control)
      enableJSRendering: true,        // Enable JavaScript execution (5x cost)
      outputFormat: 'markdown',       // AI-ready format
      waitCondition: 'networkidle2',  // Wait for network to be idle
      customWaitTime: 3000,           // Additional wait for dynamic content
      blockResources: true,           // Block CSS/images for performance
      timeout: 45000,                 // Extended timeout for JS rendering
      deviceType: 'desktop'
    });
  }

  /**
   * Enhanced scraping with fallback to residential proxy
   */
  private async enhancedScrape(url: string): Promise<ScrapeResult> {
    // First try with datacenter + browser
    const enhancedResult = await this.enhancedScrapeOnly(url);

    // If still insufficient and we have credits, try residential proxy
    if (enhancedResult.success) {
      const reAnalysis = this.contentAnalyzer.analyzeContentQuality(enhancedResult.content);

      if (!reAnalysis.needsEnhancedScraping) {
        console.log(`Enhanced scraping successful: ${reAnalysis.scenario}`);
        return enhancedResult;
      }
    }

    // Last resort: Residential proxy + browser rendering (expensive but effective)
    console.log(`Attempting residential proxy for difficult site: ${url}`);

    const finalResult = await scrapeDoClient.scrapePage(url, {
      useResidentialProxy: true,      // Residential proxy (10x base cost)
      enableJSRendering: true,        // Browser rendering (5x multiplier)
      outputFormat: 'markdown',       // AI-ready format
      waitCondition: 'networkidle2',  // Less strict network idle condition
      customWaitTime: 5000,           // Longer wait for difficult sites
      blockResources: true,           // Keep costs controlled
      timeout: 60000,                 // Extended timeout
      geoTargeting: 'us'              // US-based residential proxy
    });

    return finalResult.success ? finalResult : enhancedResult; // Return best result
  }

  /**
   * Check if content is good enough for AI processing
   */
  private isGoodEnoughContent(content: string): boolean {
    // Smart criteria for cost optimization - sufficient for AI processing
    return (
      content.length > 300 &&                    // Sufficient content for AI
      content.split(/\s+/).length > 50 &&       // Meaningful word count
      /<h[1-6]|^#{1,6}\s/m.test(content) &&    // Has at least one heading
      !this.hasErrorIndicators(content) &&      // No obvious errors
      this.calculateBasicQualityScore(content) > 60 && // Maintain quality for AI
      this.hasSufficientStructure(content)      // Ensure AI can extract meaningful data
    );
  }

  /**
   * Check if content has sufficient structure for quality AI processing
   */
  private hasSufficientStructure(content: string): boolean {
    // Ensure content has enough structure for quality AI generation
    const hasMultipleHeadings = (content.match(/<h[1-6]|^#{1,6}\s/g) || []).length >= 2;
    const hasParagraphs = content.split(/\n\s*\n/).length >= 3;
    const hasLists = /^[\*\-\+]\s|^\d+\.\s/m.test(content);
    const hasLinks = /<a\s+href|^\[.*\]\(/m.test(content);

    // Need at least 2 structural elements for quality AI processing
    const structuralElements = [hasMultipleHeadings, hasParagraphs, hasLists, hasLinks];
    return structuralElements.filter(Boolean).length >= 2;
  }

  /**
   * Calculate cost-benefit analysis for enhancement decision
   */
  private calculateCostBenefit(content: string, url: string): CostBenefitAnalysis {
    let enhancementProbability = 0;
    let expectedQualityImprovement = 0;

    // Indicators that enhancement will significantly improve AI content generation
    if (content.includes('Loading...')) enhancementProbability += 0.4;
    if (content.length < 300) enhancementProbability += 0.4; // Insufficient for quality AI
    if (this.isKnownSPASite(url)) enhancementProbability += 0.5;
    if (!/<h[1-6]/.test(content)) enhancementProbability += 0.3; // No structure for AI
    if (content.split(/\s+/).length < 50) enhancementProbability += 0.4; // Too few words
    if (!this.hasSufficientStructure(content)) enhancementProbability += 0.3; // Poor structure

    // Expected quality improvement for AI processing
    if (enhancementProbability > 0.8) expectedQualityImprovement = 8; // Massive improvement expected
    else if (enhancementProbability > 0.6) expectedQualityImprovement = 5; // Significant improvement
    else if (enhancementProbability > 0.4) expectedQualityImprovement = 3; // Moderate improvement
    else expectedQualityImprovement = 1.5; // Minimal improvement

    // Enhance if we expect significant quality improvement for AI processing
    const worthEnhancing = (enhancementProbability > 0.5) && (expectedQualityImprovement >= 3);

    return {
      enhancementProbability,
      expectedImprovement: expectedQualityImprovement,
      worthEnhancing,
      reasoning: `AI Quality Focus - Probability: ${enhancementProbability.toFixed(2)}, Expected Quality: ${expectedQualityImprovement}x improvement`
    };
  }

  /**
   * Check for error indicators in content
   */
  private hasErrorIndicators(content: string): boolean {
    const errorPatterns = [
      /404.*not found/i,
      /access denied/i,
      /forbidden/i,
      /unauthorized/i,
      /server error/i
    ];

    return errorPatterns.some(pattern => pattern.test(content));
  }

  /**
   * Calculate basic quality score for content
   */
  private calculateBasicQualityScore(content: string): number {
    let score = 0;

    // Length score (0-30 points) - more lenient
    const lengthScore = Math.min(30, (content.length / 1000) * 30);
    score += lengthScore;

    // Structure score (0-40 points) - prioritize structure
    const headingCount = (content.match(/<h[1-6]|^#{1,6}\s/g) || []).length;
    const structureScore = Math.min(40, headingCount * 10);
    score += structureScore;

    // Content diversity score (0-30 points)
    const uniqueWords = new Set(content.toLowerCase().split(/\s+/)).size;
    const diversityScore = Math.min(30, (uniqueWords / 50) * 30);
    score += diversityScore;

    return Math.round(score);
  }

  /**
   * Pattern matching methods
   */
  private matchesNeverEnhancePattern(url: string): boolean {
    return this.NEVER_ENHANCE_PATTERNS.some(pattern => pattern.test(url));
  }

  private matchesAlwaysEnhancePattern(url: string): boolean {
    return this.ALWAYS_ENHANCE_PATTERNS.some(pattern => pattern.test(url));
  }

  private isKnownSPASite(url: string): boolean {
    const spaIndicators = [/app\./i, /dashboard\./i, /admin\./i, /portal\./i];
    return spaIndicators.some(pattern => pattern.test(url));
  }

  /**
   * Categorize URLs for batch processing optimization
   */
  categorizeUrlsByPattern(urls: string[]): CategorizedUrls {
    return {
      neverEnhance: urls.filter(url => this.matchesNeverEnhancePattern(url)),
      alwaysEnhance: urls.filter(url => this.matchesAlwaysEnhancePattern(url)),
      unknown: urls.filter(url =>
        !this.matchesNeverEnhancePattern(url) &&
        !this.matchesAlwaysEnhancePattern(url)
      )
    };
  }

  /**
   * Calculate estimated savings for batch processing
   */
  calculateEstimatedSavings(categorized: CategorizedUrls): CostSavingsEstimate {
    const neverEnhanceSavings = categorized.neverEnhance.length * 4; // Save 4 credits each
    const alwaysEnhanceSavings = categorized.alwaysEnhance.length * 1; // Save 1 credit each
    const unknownCost = categorized.unknown.length * 2.5; // Estimated average

    return {
      neverEnhanceSavings,
      alwaysEnhanceSavings,
      totalEstimatedSavings: neverEnhanceSavings + alwaysEnhanceSavings,
      estimatedTotalCost: categorized.neverEnhance.length * 1 +
                         categorized.alwaysEnhance.length * 5 +
                         unknownCost,
      savingsPercentage: ((neverEnhanceSavings + alwaysEnhanceSavings) /
                         (categorized.neverEnhance.length * 5 +
                          categorized.alwaysEnhance.length * 6 +
                          categorized.unknown.length * 5)) * 100
    };
  }
}

// Export singleton instance
export const costOptimizer = new CostOptimizer();
