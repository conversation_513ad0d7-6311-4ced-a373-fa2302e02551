/**
 * TypeScript interfaces for the enhanced scrape.do API integration
 * Supports cost optimization, multi-page scraping, and media collection
 */

// Core scrape.do API interfaces
export interface ScrapeDoConfig {
  apiKey: string;
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface ScrapeOptions {
  // Proxy Configuration
  useResidentialProxy?: boolean; // Use super=true for residential/mobile
  geoTargeting?: string; // Country code for geo-targeting
  stickySession?: number; // Session ID for consistent IP

  // Browser Configuration
  enableJSRendering?: boolean; // Enable headless browser
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  waitCondition?: 'domcontentloaded' | 'load' | 'networkidle0' | 'networkidle2';
  customWaitTime?: number; // Additional wait in milliseconds
  waitForSelector?: string; // CSS selector to wait for

  // Output Configuration
  outputFormat?: 'raw' | 'markdown';
  captureScreenshot?: boolean;
  fullPageScreenshot?: boolean;
  includeNetworkRequests?: boolean;

  // Performance Optimization
  blockResources?: boolean; // Block CSS/images for faster scraping
  timeout?: number;
  retryTimeout?: number;
}

export interface ScrapeResult {
  success: boolean;
  content: string;
  metadata?: {
    creditsUsed: number;
    requestType: string;
    proxyType?: string;
    browserEnabled?: boolean;
  };
  timestamp: string;
  error?: string;
  url?: string;
}

// Content analysis interfaces
export interface ContentAnalysis {
  hasMetaTags: boolean;           // Substantial meta tag presence
  hasLoadingIndicators: boolean;  // Loading text/spinners detected
  hasSubstantialContent: boolean; // Meaningful content beyond meta/loading
  hasStructure: boolean;          // Headings, paragraphs, lists
  contentRatio: number;           // Clean content / total content ratio
  needsEnhancedScraping: boolean; // Final decision for re-scraping
  confidence: number;             // Confidence score (0-100)
  scenario?: string;              // Detected scenario for debugging
}

export interface CostBenefitAnalysis {
  enhancementProbability: number;
  expectedImprovement: number;
  worthEnhancing: boolean;
  reasoning: string;
}

// Media collection interfaces
export interface MediaAsset {
  type: 'og:image' | 'twitter:image' | 'facebook:image' | 'favicon' | 'screenshot';
  url: string;
  priority: number;
  metadata?: {
    width?: number;
    height?: number;
    format?: string;
    size?: number;
  };
}

export interface ImageCollection {
  favicon: string[] | null;
  ogImages: MediaAsset[];
  screenshot: ScreenshotResult | null;
}

export interface ScreenshotResult {
  success: boolean;
  screenshot?: string; // Base64 encoded image
  metadata?: {
    width: number;
    height: number;
    fullPage: boolean;
    capturedAt: string;
  };
  error?: string;
  timestamp: string;
}

export interface FaviconResult {
  faviconUrls: string[];
  primaryFavicon: string | null;
}

// Multi-page scraping interfaces
export interface MultiPageScrapingConfig {
  enabled: boolean;
  mode: 'immediate' | 'queue_for_later' | 'conditional';
  maxPagesPerTool: number;
  creditThreshold: number; // Minimum credits before multi-page scraping

  pageTypes: {
    pricing: PageTypeConfig;
    faq: PageTypeConfig;
    features: PageTypeConfig;
    about: PageTypeConfig;
  };

  fallbackStrategy: {
    searchInMainPage: boolean; // Look for content in main page first
    useNavigation: boolean; // Follow navigation links
    useSitemap: boolean; // Check sitemap.xml
  };
}

export interface PageTypeConfig {
  enabled: boolean;
  priority: 'high' | 'medium' | 'low';
  patterns: string[]; // URL patterns to detect pages
  selectors: string[]; // CSS selectors for content
  required: boolean; // Whether to fail if not found
}

export interface PageDiscoveryResult {
  pageType: 'pricing' | 'faq' | 'features' | 'about';
  url: string;
  confidence: number; // 0-100 confidence score
  foundMethod: 'navigation' | 'pattern' | 'content' | 'sitemap';
  priority: 'high' | 'medium' | 'low';
  estimatedCredits: number;
}

export interface ScrapingDecision {
  scrapeNow: PageDiscoveryResult[];
  queueForLater: PageDiscoveryResult[];
  skipPages: PageDiscoveryResult[];
  reason: string;
}

// Cost optimization interfaces
export interface CostSavingsEstimate {
  neverEnhanceSavings: number;
  alwaysEnhanceSavings: number;
  totalEstimatedSavings: number;
  estimatedTotalCost: number;
  savingsPercentage: number;
}

export interface CategorizedUrls {
  neverEnhance: string[];      // 1 credit each - 80% cost savings
  alwaysEnhance: string[];     // 5 credits each - skip basic attempt
  unknown: string[];           // Variable cost - intelligent detection
}

export interface BatchResult {
  totalUrls: number;
  successfulScrapes: number;
  failedScrapes: number;
  totalCreditsUsed: number;
  estimatedSavings: number;
  results: ScrapeResult[];
  processingTime: number;
}

// Usage monitoring interfaces
export interface UsageStats {
  isActive: boolean;
  concurrentRequests: number;
  maxMonthlyRequests: number;
  remainingConcurrentRequests: number;
  remainingMonthlyRequests: number;
  lastUpdated: string;
}

// Validation interfaces
export interface ValidationResult {
  isValid: boolean;
  issues: string[];
  contentLength: number;
  url: string;
  qualityScore: number;
}

// Network monitoring interfaces
export interface NetworkScrapeResult {
  content: string;
  networkRequests?: any[];
  frames?: any[];
  websockets?: any[];
  headers?: Record<string, string>;
}

export interface WebhookResponse {
  success: boolean;
  message: string;
  webhookUrl: string;
  timestamp: string;
}

// Model selection interfaces for AI integration
export interface ModelSelectionCriteria {
  contentSize: number;
  complexity: 'simple' | 'moderate' | 'complex';
  priority: 'speed' | 'quality' | 'cost';
}

export interface ModelConfig {
  provider: 'openai' | 'openrouter';
  model: string;
  maxTokens: number;
  reasoning: string;
}

// Enhanced scraping workflow interfaces
export interface EnhancedScrapeRequest {
  url: string;
  options: ScrapeOptions;
  multiPageConfig?: MultiPageScrapingConfig;
  mediaCollection?: boolean;
  costOptimization?: boolean;
}

export interface EnhancedScrapeResult extends ScrapeResult {
  mediaAssets?: ImageCollection;
  additionalPages?: ScrapeResult[];
  costAnalysis?: {
    creditsUsed: number;
    estimatedSavings: number;
    optimizationStrategy: string;
  };
  contentAnalysis?: ContentAnalysis;
}

// Error handling interfaces
export interface ScrapeError {
  code: string;
  message: string;
  url: string;
  timestamp: string;
  retryable: boolean;
  context?: any;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}
