/**
 * Scrape.do API Client
 * Provides cost-optimized web scraping with intelligent content analysis
 */

import {
  ScrapeDoConfig,
  ScrapeOptions,
  ScrapeResult,
  ScrapeError,
  RetryConfig,
  UsageStats
} from './types';

export class ScrapeDoClient {
  private config: ScrapeDoConfig;
  private retryConfig: RetryConfig;

  constructor() {
    this.config = {
      apiKey: process.env.SCRAPE_DO_API_KEY || '8e7e405ff81145c4afe447610ddb9a7f785f494dddc',
      baseUrl: 'https://api.scrape.do',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 2000
    };

    this.retryConfig = {
      maxAttempts: 3,
      baseDelay: 2000,
      maxDelay: 10000,
      backoffMultiplier: 2
    };
  }

  /**
   * Main scraping method with cost optimization
   */
  async scrapePage(url: string, options: ScrapeOptions = {}): Promise<ScrapeResult> {
    const params = this.buildRequestParams(url, options);
    const requestUrl = `${this.config.baseUrl}/?${params.toString()}`;

    try {
      const response = await this.makeRequest(requestUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();
      const metadata = this.extractMetadata(response.headers, options);

      return {
        success: true,
        content,
        metadata,
        timestamp: new Date().toISOString(),
        url
      };
    } catch (error) {
      return this.handleScrapeError(error as Error, url, options);
    }
  }

  /**
   * Build URL parameters for scrape.do API
   */
  private buildRequestParams(url: string, options: ScrapeOptions): URLSearchParams {
    const params = new URLSearchParams({
      token: this.config.apiKey,
      url: url // URL will be encoded by URLSearchParams
    });

    // Add optional parameters based on options
    if (options.useResidentialProxy) params.set('super', 'true');
    if (options.geoTargeting) params.set('geoCode', options.geoTargeting);
    if (options.stickySession) params.set('sessionId', options.stickySession.toString());
    if (options.enableJSRendering) params.set('render', 'true');
    if (options.deviceType) params.set('device', options.deviceType);
    if (options.waitCondition) params.set('waitUntil', options.waitCondition);
    if (options.customWaitTime) params.set('customWait', options.customWaitTime.toString());
    if (options.waitForSelector) params.set('waitSelector', options.waitForSelector);
    if (options.outputFormat) params.set('output', options.outputFormat);
    if (options.captureScreenshot) params.set('screenShot', 'true');
    if (options.fullPageScreenshot) params.set('fullScreenShot', 'true');
    if (options.includeNetworkRequests) params.set('returnJSON', 'true');
    if (options.blockResources !== undefined) params.set('blockResources', options.blockResources.toString());
    if (options.timeout) params.set('timeout', options.timeout.toString());
    if (options.retryTimeout) params.set('retryTimeout', options.retryTimeout.toString());

    return params;
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequest(url: string): Promise<Response> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.retryConfig.maxAttempts; attempt++) {
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br'
          },
          signal: AbortSignal.timeout(this.config.timeout)
        });

        return response;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.retryConfig.maxAttempts) {
          const delay = Math.min(
            this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 1),
            this.retryConfig.maxDelay
          );
          
          console.log(`Scrape attempt ${attempt} failed, retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * Extract metadata from response headers
   */
  private extractMetadata(headers: Headers, options: ScrapeOptions) {
    const creditsUsed = this.calculateCreditsUsed(options);
    const requestType = this.getRequestType(options);

    return {
      creditsUsed,
      requestType,
      proxyType: options.useResidentialProxy ? 'residential' : 'datacenter',
      browserEnabled: options.enableJSRendering || false
    };
  }

  /**
   * Calculate credits used based on options
   */
  private calculateCreditsUsed(options: ScrapeOptions): number {
    let credits = 1; // Base cost for datacenter proxy

    if (options.useResidentialProxy) {
      credits = 10; // Residential/Mobile proxy base cost
    }

    if (options.enableJSRendering) {
      credits *= 5; // 5x multiplier for headless browser
    }

    return credits;
  }

  /**
   * Determine request type for logging
   */
  private getRequestType(options: ScrapeOptions): string {
    if (options.useResidentialProxy && options.enableJSRendering) {
      return 'Residential + Browser';
    } else if (options.useResidentialProxy) {
      return 'Residential Proxy';
    } else if (options.enableJSRendering) {
      return 'Datacenter + Browser';
    } else {
      return 'Datacenter Proxy';
    }
  }

  /**
   * Handle scraping errors with proper error classification
   */
  private handleScrapeError(error: Error, url: string, options: ScrapeOptions): ScrapeResult {
    const scrapeError: ScrapeError = {
      code: this.classifyError(error),
      message: error.message,
      url,
      timestamp: new Date().toISOString(),
      retryable: this.isRetryableError(error),
      context: { options }
    };

    console.error('Scraping error:', scrapeError);

    return {
      success: false,
      content: '',
      error: error.message,
      timestamp: new Date().toISOString(),
      url
    };
  }

  /**
   * Classify error types for better handling
   */
  private classifyError(error: Error): string {
    if (error.name === 'AbortError') return 'TIMEOUT';
    if (error.message.includes('fetch')) return 'NETWORK_ERROR';
    if (error.message.includes('HTTP 4')) return 'CLIENT_ERROR';
    if (error.message.includes('HTTP 5')) return 'SERVER_ERROR';
    return 'UNKNOWN_ERROR';
  }

  /**
   * Determine if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryableCodes = ['TIMEOUT', 'NETWORK_ERROR', 'SERVER_ERROR'];
    return retryableCodes.includes(this.classifyError(error));
  }

  /**
   * Get current usage statistics
   */
  async getUsageStatistics(): Promise<UsageStats> {
    try {
      const response = await fetch(`${this.config.baseUrl}/info/?token=${this.config.apiKey}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch usage stats: ${response.status}`);
      }

      const stats = await response.json();

      return {
        isActive: stats.IsActive,
        concurrentRequests: stats.ConcurrentRequest,
        maxMonthlyRequests: stats.MaxMonthlyRequest,
        remainingConcurrentRequests: stats.RemainingConcurrentRequest,
        remainingMonthlyRequests: stats.RemainingMonthlyRequest,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to get usage statistics:', error);
      throw error;
    }
  }

  /**
   * Monitor usage and provide warnings
   */
  async monitorUsage(): Promise<void> {
    try {
      const stats = await this.getUsageStatistics();

      const monthlyUsagePercent = ((stats.maxMonthlyRequests - stats.remainingMonthlyRequests) / stats.maxMonthlyRequests) * 100;
      const concurrentUsagePercent = ((stats.concurrentRequests - stats.remainingConcurrentRequests) / stats.concurrentRequests) * 100;

      if (monthlyUsagePercent > 80) {
        console.warn(`Monthly usage at ${monthlyUsagePercent.toFixed(1)}% - consider upgrading plan`);
      }

      if (concurrentUsagePercent > 90) {
        console.warn(`Concurrent requests at ${concurrentUsagePercent.toFixed(1)}% - reduce parallel processing`);
      }
    } catch (error) {
      console.error('Usage monitoring failed:', error);
    }
  }

  /**
   * Resolve relative URLs to absolute URLs
   */
  resolveUrl(relativeUrl: string, baseUrl: string): string {
    try {
      return new URL(relativeUrl, baseUrl).href;
    } catch {
      return relativeUrl;
    }
  }
}

// Export singleton instance
export const scrapeDoClient = new ScrapeDoClient();
